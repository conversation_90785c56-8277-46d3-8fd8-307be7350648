import React, { useState } from 'react'
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Container,
  Avatar
} from '@mui/material'
import { LockOutlined } from '@mui/icons-material'
import { useAuth } from '../contexts/AuthContext'
import { useNavigate } from 'react-router-dom'
import { useSnackbar } from 'notistack'
import CopyButton from '../components/common/CopyButton'

const LoginPage = () => {
  const [formData, setFormData] = useState({
    loginName: '',
    password: '',
    deviceId: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [approvalDialog, setApprovalDialog] = useState({
    open: false,
    userId: null,
    deviceId: ''
  })

  const { login, approveDevice } = useAuth()
  const navigate = useNavigate()
  const { enqueueSnackbar } = useSnackbar()

  // توليد معرف جهاز فريد (بدون بادئة device_)
  React.useEffect(() => {
    const generateDeviceId = () => {
      let deviceId = localStorage.getItem('deviceId')

      // إذا كان هناك رقم جهاز محفوظ
      if (deviceId) {
        // إزالة البادئة device_ إذا وجدت
        if (deviceId.startsWith('device_')) {
          deviceId = deviceId.replace('device_', '')
          localStorage.setItem('deviceId', deviceId) // حفظ الرقم الجديد بدون بادئة
        }
        setFormData(prev => ({ ...prev, deviceId }))
      } else {
        // إنشاء رقم جهاز جديد بدون بادئة
        const newDeviceId = Math.random().toString(36).substring(2, 11) + '_' + Date.now()
        localStorage.setItem('deviceId', newDeviceId)
        setFormData(prev => ({ ...prev, deviceId: newDeviceId }))
      }
    }
    generateDeviceId()
  }, [])

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
    setError('')
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const result = await login(formData.loginName, formData.password, formData.deviceId)
      if (result.success) {
        enqueueSnackbar('تم تسجيل الدخول بنجاح!', { variant: 'success' })
        navigate('/')
      } else {
        let errorMessage = result.error || 'حدث خطأ في تسجيل الدخول'

        // إضافة تفاصيل إضافية للأخطاء المتعلقة بالأجهزة
        if (result.details && result.details.authorizedDevices) {
          errorMessage += `\n\nالأجهزة المسموحة: ${result.details.authorizedDevices.join(', ')}\nالجهاز الحالي: ${result.details.currentDevice}`
        }

        setError(errorMessage)
        enqueueSnackbar(result.error || 'فشل في تسجيل الدخول', { variant: 'error' })
      }
    } catch (error) {
      console.error('Login error:', error)
      setError('حدث خطأ في تسجيل الدخول')
      enqueueSnackbar('حدث خطأ في تسجيل الدخول', { variant: 'error' })
    } finally {
      setLoading(false)
    }
  }

  const handleApproveDevice = async () => {
    const result = await approveDevice(approvalDialog.userId, approvalDialog.deviceId)

    if (result.success) {
      enqueueSnackbar('تم الموافقة على الجهاز بنجاح. يمكنك الآن تسجيل الدخول.', { variant: 'success' })
      setApprovalDialog({ open: false, userId: null, deviceId: '' })
    } else {
      enqueueSnackbar(result.error, { variant: 'error' })
    }
  }

  return (
    <Container component="main" maxWidth="sm">
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          py: 4
        }}
      >
        <Card sx={{ width: '100%', maxWidth: 400 }}>
          <CardContent sx={{ p: 4 }}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                mb: 3
              }}
            >
              <Avatar sx={{ m: 1, bgcolor: 'primary.main', width: 56, height: 56 }}>
                <LockOutlined />
              </Avatar>
              <Typography component="h1" variant="h4" sx={{ fontWeight: 700, color: 'primary.main' }}>
                نظام إدارة العملاء
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                تسجيل الدخول إلى لوحة التحكم
              </Typography>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            <Box component="form" onSubmit={handleSubmit} sx={{ mt: 1 }}>
              <TextField
                margin="normal"
                required
                fullWidth
                id="loginName"
                label="اسم المستخدم"
                name="loginName"
                autoComplete="username"
                autoFocus
                value={formData.loginName}
                onChange={handleChange}
                disabled={loading}
              />
              <TextField
                margin="normal"
                required
                fullWidth
                name="password"
                label="كلمة المرور"
                type="password"
                id="password"
                autoComplete="current-password"
                value={formData.password}
                onChange={handleChange}
                disabled={loading}
              />

              {/* عرض رقم الجهاز مع زر النسخ */}
              <Box sx={{
                mt: 2,
                p: 2,
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 1,
                backgroundColor: 'background.paper',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between'
              }}>
                <Box>
                  <Typography variant="body2" fontWeight="medium" color="text.primary">
                    رقم الجهاز:
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      fontFamily: 'monospace',
                      backgroundColor: 'grey.100',
                      px: 1,
                      py: 0.5,
                      borderRadius: 0.5,
                      border: '1px solid',
                      borderColor: 'grey.300',
                      mt: 0.5
                    }}
                  >
                    {formData.deviceId}
                  </Typography>
                </Box>
                <CopyButton
                  text={formData.deviceId}
                  tooltip="نسخ الكود"
                  successMessage="تم نسخ رقم الجهاز بنجاح!"
                  errorMessage="فشل في نسخ رقم الجهاز"
                />
              </Box>

              <Button
                type="submit"
                fullWidth
                variant="contained"
                sx={{ mt: 3, mb: 2, py: 1.5 }}
                disabled={loading}
              >
                {loading ? (
                  <CircularProgress size={24} color="inherit" />
                ) : (
                  'تسجيل الدخول'
                )}
              </Button>
            </Box>
          </CardContent>
        </Card>

        {/* Dialog للموافقة على الجهاز */}
        <Dialog open={approvalDialog.open} onClose={() => setApprovalDialog({ open: false, userId: null, deviceId: '' })}>
          <DialogTitle>الموافقة على جهاز جديد</DialogTitle>
          <DialogContent>
            <Typography>
              هذا الجهاز غير مصرح له بالوصول. هل تريد الموافقة على هذا الجهاز؟
            </Typography>
            <Typography variant="caption" sx={{ mt: 2, display: 'block' }}>
              معرف الجهاز: {approvalDialog.deviceId}
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setApprovalDialog({ open: false, userId: null, deviceId: '' })}>
              إلغاء
            </Button>
            <Button onClick={handleApproveDevice} variant="contained">
              موافقة
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Container>
  )
}

export default LoginPage
