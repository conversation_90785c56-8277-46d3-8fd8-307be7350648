const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const session = require('express-session');
const cookieParser = require('cookie-parser');
const path = require('path');
const fs = require('fs');
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');
require('dotenv').config();

const app = express();
const PORT = 8080;
const prisma = new PrismaClient();

console.log('🚀 Starting Working Server...');
console.log('📊 Database URL:', process.env.DATABASE_URL ? 'Configured' : 'Not configured');

// اختبار الاتصال بقاعدة البيانات
async function testDatabaseConnection() {
  try {
    console.log('🔗 Testing database connection...');
    await prisma.$connect();
    await prisma.$queryRaw`SELECT 1`;
    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

// Basic middleware
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:8080',
    'http://**************:8080',
    'http://************:8080',
    'http://***********:8080'
  ],
  credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());
app.use(morgan('combined'));

// Session configuration
app.use(session({
  secret: process.env.SESSION_SECRET || 'yemclient-super-secret-key-2024',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: false,
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// Logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Health check مع فحص قاعدة البيانات
app.get('/health', async (req, res) => {
  try {
    console.log('Health check requested');

    // فحص قاعدة البيانات
    await prisma.$queryRaw`SELECT 1`;

    // إحصائيات سريعة
    const [userCount, clientCount, agentCount] = await Promise.all([
      prisma.user.count().catch(() => 0),
      prisma.client.count().catch(() => 0),
      prisma.agent.count().catch(() => 0)
    ]);

    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: '1.0.0',
      database: {
        status: 'connected',
        users: userCount,
        clients: clientCount,
        agents: agentCount
      },
      message: 'Server is working with real database!'
    };

    console.log('✅ Health check successful with database');
    res.json(healthData);
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
    res.status(500).json({
      status: 'unhealthy',
      error: error.message,
      database: 'disconnected',
      message: 'Server has database issues'
    });
  }
});

// API endpoints
app.get('/api/test', (req, res) => {
  res.json({ message: 'API is working!' });
});

// ==================== AUTH API المشترك ====================
app.post('/api/auth/login', async (req, res) => {
  try {
    const { loginName, password, deviceId, userType = 'auto' } = req.body;
    console.log('🔐 Login attempt:', { loginName, userType, deviceId });

    if (!loginName || !password) {
      console.log('❌ Missing credentials');
      return res.status(400).json({
        success: false,
        message: 'اسم المستخدم وكلمة المرور مطلوبان'
      });
    }

    let user = null;
    let accountType = null;

    // البحث في جدول المستخدمين أولاً
    if (userType === 'auto' || userType === 'user') {
      try {
        user = await prisma.user.findUnique({
          where: { loginName }
        });
        if (user) {
          accountType = 'user';
          console.log('👤 Found user account:', user.username);
        }
      } catch (error) {
        console.log('⚠️ Error searching users:', error.message);
      }
    }

    // البحث في جدول العملاء إذا لم نجد مستخدم
    if (!user && (userType === 'auto' || userType === 'client')) {
      try {
        user = await prisma.client.findFirst({
          where: {
            OR: [
              { clientCode: parseInt(loginName) || 0 },
              { cardNumber: loginName }
            ]
          }
        });
        if (user) {
          accountType = 'client';
          console.log('🏢 Found client account:', user.clientName);
        }
      } catch (error) {
        console.log('⚠️ Error searching clients:', error.message);
      }
    }

    if (!user) {
      console.log('❌ Account not found:', loginName);

      // تسجيل محاولة دخول فاشلة
      try {
        await prisma.loginAttempt.create({
          data: {
            userType: accountType || 'unknown',
            deviceId: deviceId || 'unknown',
            ipAddress: req.ip || 'unknown',
            userAgent: req.get('User-Agent') || 'unknown',
            success: false
          }
        });
      } catch (logError) {
        console.log('⚠️ Failed to log failed attempt:', logError.message);
      }

      return res.status(401).json({
        success: false,
        message: 'بيانات الدخول غير صحيحة'
      });
    }

    // التحقق من كلمة المرور
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      console.log('❌ Invalid password for:', loginName);

      // تسجيل محاولة دخول فاشلة
      try {
        await prisma.loginAttempt.create({
          data: {
            userType: accountType,
            userId: accountType === 'user' ? user.id : null,
            clientId: accountType === 'client' ? user.id : null,
            deviceId: deviceId || 'unknown',
            ipAddress: req.ip || 'unknown',
            userAgent: req.get('User-Agent') || 'unknown',
            success: false
          }
        });
      } catch (logError) {
        console.log('⚠️ Failed to log failed attempt:', logError.message);
      }

      return res.status(401).json({
        success: false,
        message: 'بيانات الدخول غير صحيحة'
      });
    }

    // التحقق من حالة الحساب
    const isActive = accountType === 'user' ? user.isActive : user.status === 1;
    if (!isActive) {
      console.log('❌ Account inactive:', loginName);
      return res.status(403).json({
        success: false,
        message: 'الحساب غير مفعل'
      });
    }

    // تسجيل محاولة دخول ناجحة
    try {
      await prisma.loginAttempt.create({
        data: {
          userType: accountType,
          userId: accountType === 'user' ? user.id : null,
          clientId: accountType === 'client' ? user.id : null,
          deviceId: deviceId || 'unknown',
          ipAddress: req.ip || 'unknown',
          userAgent: req.get('User-Agent') || 'unknown',
          success: true
        }
      });
    } catch (logError) {
      console.log('⚠️ Failed to log successful attempt:', logError.message);
    }

    // إعداد بيانات الاستجابة
    let responseData;
    if (accountType === 'user') {
      responseData = {
        id: user.id,
        username: user.username,
        loginName: user.loginName,
        permissions: user.permissions || {},
        deviceId: user.deviceId,
        isActive: user.isActive,
        accountType: 'user'
      };
    } else {
      responseData = {
        id: user.id,
        username: user.clientName,
        loginName: user.clientCode.toString(),
        clientCode: user.clientCode,
        appName: user.appName,
        isActive: user.status === 1,
        accountType: 'client'
      };
    }

    console.log('✅ Login successful:', {
      accountType,
      id: user.id,
      name: accountType === 'user' ? user.username : user.clientName
    });

    // حفظ في الجلسة
    req.session.user = responseData;

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: responseData
    });

  } catch (error) {
    console.error('❌ Login error:', error.message);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم'
    });
  }
});

// ==================== USERS API ====================
app.get('/api/users', async (req, res) => {
  try {
    console.log('📋 Users list requested');

    const { page = 1, limit = 10, search = '' } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const where = search ? {
      OR: [
        { username: { contains: search, mode: 'insensitive' } },
        { loginName: { contains: search, mode: 'insensitive' } }
      ]
    } : {};

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip,
        take: parseInt(limit),
        select: {
          id: true,
          username: true,
          loginName: true,
          deviceId: true,
          device1: true,
          permissions: true,
          isActive: true,
          createdAt: true,
          _count: {
            select: { clients: true }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.user.count({ where })
    ]);

    console.log(`✅ Users list generated: ${users.length}/${total}`);

    res.json({
      users,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('❌ Users list error:', error.message);
    res.status(500).json({ error: 'فشل في جلب قائمة المستخدمين' });
  }
});

// ==================== CLIENTS API ====================
app.get('/api/clients', async (req, res) => {
  try {
    console.log('📋 Clients list requested');

    const { page = 1, limit = 10, search = '', status, userId } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const where = {};

    if (search) {
      where.OR = [
        { clientName: { contains: search, mode: 'insensitive' } },
        { clientCode: { equals: parseInt(search) || 0 } },
        { cardNumber: { contains: search } }
      ];
    }

    if (status) {
      where.status = parseInt(status);
    }

    if (userId) {
      where.userId = parseInt(userId);
    }

    const [clients, total] = await Promise.all([
      prisma.client.findMany({
        where,
        skip,
        take: parseInt(limit),
        include: {
          user: {
            select: {
              id: true,
              username: true,
              loginName: true
            }
          },
          _count: {
            select: { dataRecords: true }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.client.count({ where })
    ]);

    console.log(`✅ Clients list generated: ${clients.length}/${total}`);

    res.json({
      clients,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('❌ Clients list error:', error.message);
    res.status(500).json({ error: 'فشل في جلب قائمة العملاء' });
  }
});

app.post('/api/clients', async (req, res) => {
  try {
    const { clientName, appName, cardNumber, password, ipAddress, userId } = req.body;
    console.log('🆕 Create client attempt:', clientName);

    // التحقق من البيانات المطلوبة
    if (!clientName || !appName || !cardNumber || !password || !userId) {
      return res.status(400).json({
        error: 'جميع البيانات مطلوبة'
      });
    }

    // التحقق من عدم تكرار رقم البطاقة
    const existingClient = await prisma.client.findFirst({
      where: { cardNumber }
    });

    if (existingClient) {
      return res.status(409).json({
        error: 'رقم البطاقة موجود مسبقاً'
      });
    }

    // إنشاء رقم عميل تلقائي
    const lastClient = await prisma.client.findFirst({
      orderBy: { clientCode: 'desc' }
    });
    const clientCode = lastClient ? lastClient.clientCode + 1 : 1000;

    // تشفير كلمة المرور
    const hashedPassword = await bcrypt.hash(password, 10);

    // إنشاء العميل
    const newClient = await prisma.client.create({
      data: {
        clientName,
        appName,
        cardNumber,
        clientCode,
        password: hashedPassword,
        ipAddress: ipAddress || req.ip,
        userId: parseInt(userId),
        status: 1
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            loginName: true
          }
        }
      }
    });

    console.log('✅ Client created:', {
      clientId: newClient.id,
      clientCode: newClient.clientCode,
      clientName
    });

    res.status(201).json({
      success: true,
      message: 'تم إنشاء العميل بنجاح',
      client: newClient
    });

  } catch (error) {
    console.error('❌ Create client error:', error.message);
    res.status(500).json({ error: 'فشل في إنشاء العميل' });
  }
});

// ==================== AGENTS API ====================
app.get('/api/agents', async (req, res) => {
  try {
    console.log('📋 Agents list requested');

    const { page = 1, limit = 10, search = '', agencyType } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const where = {};

    if (search) {
      where.OR = [
        { agentName: { contains: search, mode: 'insensitive' } },
        { agencyName: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (agencyType) {
      where.agencyType = agencyType;
    }

    const [agents, total] = await Promise.all([
      prisma.agent.findMany({
        where,
        skip,
        take: parseInt(limit),
        include: {
          _count: {
            select: { dataRecords: true }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.agent.count({ where })
    ]);

    console.log(`✅ Agents list generated: ${agents.length}/${total}`);

    res.json({
      agents,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('❌ Agents list error:', error.message);
    res.status(500).json({ error: 'فشل في جلب قائمة الوكلاء' });
  }
});

// ==================== DATA RECORDS API ====================
app.get('/api/data-records', async (req, res) => {
  try {
    console.log('📋 Data records requested');

    const { page = 1, limit = 10, search = '', status } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const where = {};

    if (search) {
      where.OR = [
        { clientCode: { contains: search, mode: 'insensitive' } },
        { agentReference: { equals: parseInt(search) || 0 } }
      ];
    }

    if (status) {
      where.operationStatus = parseInt(status);
    }

    const [dataRecords, total] = await Promise.all([
      prisma.dataRecord.findMany({
        where,
        skip,
        take: parseInt(limit),
        include: {
          agent: {
            select: {
              id: true,
              agentName: true,
              agencyName: true,
              agencyType: true
            }
          },
          client: {
            select: {
              id: true,
              clientName: true,
              clientCode: true,
              appName: true
            }
          }
        },
        orderBy: { operationDate: 'desc' }
      }),
      prisma.dataRecord.count({ where })
    ]);

    console.log(`✅ Data records generated: ${dataRecords.length}/${total}`);

    res.json({
      dataRecords,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('❌ Data records error:', error.message);
    res.status(500).json({ error: 'فشل في جلب سجلات البيانات' });
  }
});

// ==================== SECURITY API ====================
app.get('/api/security/stats', async (req, res) => {
  try {
    console.log('🔒 Security stats requested');

    const [totalUsers, activeUsers, totalClients, activeClients, totalAgents, activeAgents, totalAttempts, successfulAttempts] = await Promise.all([
      prisma.user.count(),
      prisma.user.count({ where: { isActive: true } }),
      prisma.client.count(),
      prisma.client.count({ where: { status: 1 } }),
      prisma.agent.count(),
      prisma.agent.count({ where: { isActive: true } }),
      prisma.loginAttempt.count(),
      prisma.loginAttempt.count({ where: { success: true } })
    ]);

    const stats = {
      totalAttempts,
      successfulLogins: successfulAttempts,
      failedLogins: totalAttempts - successfulAttempts,
      suspiciousActivity: 0,
      blockedIPs: 0,
      activeDevices: activeUsers,
      uniqueIPs: activeUsers + activeAgents,
      lastSecurityScan: new Date().toISOString(),
      last24Hours: {
        successfulLogins: successfulAttempts,
        failedAttempts: Math.floor((totalAttempts - successfulAttempts) * 0.1)
      },
      systemHealth: {
        database: 'connected',
        api: 'operational',
        security: 'active'
      },
      summary: {
        totalUsers,
        activeUsers,
        totalClients,
        activeClients,
        totalAgents,
        activeAgents
      }
    };

    console.log('✅ Security stats generated');
    res.json(stats);
  } catch (error) {
    console.error('❌ Security stats error:', error.message);
    res.status(500).json({ error: 'Failed to fetch security stats' });
  }
});

app.get('/api/security/login-attempts', async (req, res) => {
  try {
    console.log('🔒 Login attempts requested');

    const { page = 1, limit = 20 } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const [attempts, total] = await Promise.all([
      prisma.loginAttempt.findMany({
        skip,
        take: parseInt(limit),
        include: {
          user: { select: { username: true, loginName: true } },
          agent: { select: { agentName: true } }
        },
        orderBy: { timestamp: 'desc' }
      }),
      prisma.loginAttempt.count()
    ]);

    const formattedAttempts = attempts.map(attempt => ({
      id: attempt.id,
      type: attempt.success ? 'success' : 'failed',
      username: attempt.user?.username || attempt.agent?.agentName || 'Unknown',
      loginName: attempt.user?.loginName || 'N/A',
      ip: attempt.ipAddress,
      userAgent: attempt.userAgent || 'Browser',
      timestamp: attempt.timestamp.toISOString(),
      deviceId: attempt.deviceId,
      userType: attempt.userType,
      reason: attempt.success ? null : 'Invalid credentials'
    }));

    console.log(`✅ Login attempts generated: ${formattedAttempts.length}/${total}`);

    res.json({
      attempts: formattedAttempts,
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(total / parseInt(limit))
    });
  } catch (error) {
    console.error('❌ Login attempts error:', error.message);
    res.status(500).json({ error: 'Failed to fetch login attempts' });
  }
});

// ==================== EXTERNAL API ====================
app.get('/api/external/health', async (req, res) => {
  try {
    console.log('🌐 External API health check requested');

    await prisma.$queryRaw`SELECT 1`;

    res.json({
      status: 'success',
      message: 'External API is healthy',
      data: {
        timestamp: new Date().toISOString(),
        database: 'connected',
        version: '1.0.0',
        uptime: process.uptime()
      }
    });
  } catch (error) {
    console.error('❌ External API health error:', error.message);
    res.status(500).json({
      status: 'error',
      message: 'External API health check failed',
      error_code: 'HEALTH_CHECK_FAILED'
    });
  }
});

app.get('/api/external/stats', async (req, res) => {
  try {
    console.log('🌐 External API stats requested');

    const [totalClients, activeClients, totalAgents, activeAgents, totalUsers, activeUsers, totalOperations] = await Promise.all([
      prisma.client.count(),
      prisma.client.count({ where: { status: 1 } }),
      prisma.agent.count(),
      prisma.agent.count({ where: { isActive: true } }),
      prisma.user.count(),
      prisma.user.count({ where: { isActive: true } }),
      prisma.dataRecord.count()
    ]);

    const response = {
      success: true,
      message: 'System statistics retrieved successfully',
      data: {
        clients: {
          total: totalClients,
          active: activeClients,
          inactive: totalClients - activeClients
        },
        agents: {
          total: totalAgents,
          active: activeAgents,
          inactive: totalAgents - activeAgents
        },
        users: {
          total: totalUsers,
          active: activeUsers,
          inactive: totalUsers - activeUsers
        },
        operations: {
          totalRecords: totalOperations
        },
        timestamp: new Date().toISOString()
      }
    };

    console.log('✅ External API stats generated');
    res.json(response);
  } catch (error) {
    console.error('❌ External API stats error:', error.message);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve system statistics',
      error_code: 'STATS_ERROR'
    });
  }
});

// Static files for React app
const clientDistPath = path.join(__dirname, '../client/dist');
console.log('Client dist path:', clientDistPath);

// Check if client dist exists
if (fs.existsSync(clientDistPath)) {
  console.log('✅ Client dist folder found');
  app.use(express.static(clientDistPath));

  // React app fallback
  app.get('*', (req, res) => {
    // Skip API routes
    if (req.path.startsWith('/api/')) {
      return res.status(404).json({ error: 'API endpoint not found' });
    }

    console.log('Serving React app for:', req.path);
    res.sendFile(path.join(clientDistPath, 'index.html'));
  });
} else {
  console.log('❌ Client dist folder not found');

  // Fallback HTML
  app.get('*', (req, res) => {
    if (req.path.startsWith('/api/')) {
      return res.status(404).json({ error: 'API endpoint not found' });
    }

    res.send(`
      <!DOCTYPE html>
      <html lang="ar" dir="rtl">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>نظام إدارة العملاء والوكلاء</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
          }
          .container {
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            max-width: 600px;
            margin: 0 auto;
          }
          h1 { font-size: 2.5em; margin-bottom: 20px; }
          p { font-size: 1.2em; margin-bottom: 15px; }
          .status { color: #4CAF50; font-weight: bold; }
          .links { margin-top: 30px; }
          .links a {
            display: inline-block;
            margin: 10px;
            padding: 12px 24px;
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s;
          }
          .links a:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
          }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>🚀 نظام إدارة العملاء والوكلاء</h1>
          <p class="status">✅ الخادم يعمل بشكل صحيح</p>
          <p>مرحباً بك في نظام إدارة العملاء والوكلاء اليمني</p>
          <p>الخادم متصل وجاهز للاستخدام</p>

          <div class="links">
            <a href="/health">فحص الصحة</a>
            <a href="/api/test">اختبار API</a>
            <a href="/developer-test">صفحة المطورين</a>
          </div>

          <div style="margin-top: 30px; font-size: 0.9em; opacity: 0.8;">
            <p>الوقت: ${new Date().toLocaleString('ar-SA')}</p>
            <p>المنفذ: ${PORT}</p>
          </div>
        </div>

        <script>
          // Test API connection
          fetch('/health')
            .then(response => response.json())
            .then(data => {
              console.log('Health check:', data);
            })
            .catch(error => {
              console.error('Health check failed:', error);
            });
        </script>
      </body>
      </html>
    `);
  });
}

// Error handling
app.use((error, req, res, next) => {
  console.error('Server error:', error.message);
  res.status(500).json({
    error: 'Internal server error',
    message: error.message
  });
});

// إنشاء المستخدم الإداري إذا لم يكن موجوداً
async function ensureAdminUser() {
  try {
    console.log('👤 Checking for admin user...');

    const adminUser = await prisma.user.findUnique({
      where: { loginName: 'admin' }
    });

    if (!adminUser) {
      console.log('🆕 Creating admin user...');

      const hashedPassword = await bcrypt.hash('admin123456', 10);

      const newAdmin = await prisma.user.create({
        data: {
          username: 'المدير العام',
          loginName: 'admin',
          password: hashedPassword,
          deviceId: 'admin-device-001',
          device1: 'admin-device-001',
          permissions: {
            isAdmin: true,
            users: { read: true, write: true, delete: true },
            clients: { read: true, write: true, delete: true },
            agents: { read: true, write: true, delete: true },
            data: { read: true, write: true, delete: true },
            security: { read: true, write: true, delete: true }
          },
          isActive: true
        }
      });

      console.log('✅ Admin user created:', { id: newAdmin.id, username: newAdmin.username });
    } else {
      console.log('✅ Admin user already exists:', { id: adminUser.id, username: adminUser.username });
    }
  } catch (error) {
    console.error('❌ Failed to ensure admin user:', error.message);
  }
}

// بدء الخادم
async function startServer() {
  try {
    console.log('🚀 Starting Enhanced Working Server...');

    // اختبار الاتصال بقاعدة البيانات
    const dbConnected = await testDatabaseConnection();

    if (dbConnected) {
      // إنشاء المستخدم الإداري
      await ensureAdminUser();
    }

    // بدء الخادم
    app.listen(PORT, '0.0.0.0', () => {
      console.log(`✅ Enhanced Working Server started successfully!`);
      console.log(`🌐 Local: http://localhost:${PORT}`);
      console.log(`🌍 External: http://***********:${PORT}`);
      console.log(`📋 Health: http://localhost:${PORT}/health`);
      console.log(`🧪 API Test: http://localhost:${PORT}/api/test`);
      console.log(`📊 Database: ${dbConnected ? '✅ Connected' : '❌ Disconnected'}`);
      console.log(`🔐 Login: admin / admin123456`);
      console.log(`⏰ Started at: ${new Date().toLocaleString('ar-SA')}`);
      console.log(`🎯 Ready for real database operations!`);
    });

  } catch (error) {
    console.error('❌ Failed to start server:', error.message);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Server shutting down gracefully...');
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Server shutting down gracefully...');
  await prisma.$disconnect();
  process.exit(0);
});

// بدء الخادم
startServer();

module.exports = app;
