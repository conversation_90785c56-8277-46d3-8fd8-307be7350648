const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = 8080;

console.log('🚀 Starting Working Server...');

// Basic middleware
app.use(cors({
  origin: '*',
  credentials: true
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Health check
app.get('/health', (req, res) => {
  console.log('Health check requested');
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    message: 'Server is working!'
  });
});

// API endpoints
app.get('/api/test', (req, res) => {
  res.json({ message: 'API is working!' });
});

// Auth API
app.post('/api/auth/login', (req, res) => {
  const { loginName, password } = req.body;
  console.log('Login attempt:', loginName);

  // Simple test login
  if (loginName === 'admin' && password === 'admin123456') {
    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: {
        id: 1,
        username: 'المدير العام',
        loginName: 'admin',
        permissions: { isAdmin: true },
        isActive: true
      }
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'بيانات الدخول غير صحيحة'
    });
  }
});

// Users API
app.get('/api/users', (req, res) => {
  res.json({
    users: [
      {
        id: 1,
        username: 'المدير العام',
        loginName: 'admin',
        permissions: { isAdmin: true },
        isActive: true,
        createdAt: new Date().toISOString()
      }
    ],
    pagination: {
      page: 1,
      limit: 10,
      total: 1,
      totalPages: 1
    }
  });
});

// Clients API
app.get('/api/clients', (req, res) => {
  res.json({
    clients: [
      {
        id: 1,
        clientName: 'عميل تجريبي',
        clientCode: 1000,
        appName: 'تطبيق تجريبي',
        status: 1,
        createdAt: new Date().toISOString()
      }
    ],
    pagination: {
      page: 1,
      limit: 10,
      total: 1,
      totalPages: 1
    }
  });
});

app.post('/api/clients', (req, res) => {
  const { clientName, appName, cardNumber, password, userId } = req.body;
  console.log('Create client attempt:', clientName);

  if (!clientName || !appName || !cardNumber || !password || !userId) {
    return res.status(400).json({
      error: 'جميع البيانات مطلوبة'
    });
  }

  // Check if card number already exists (simulate)
  if (cardNumber === '12345678') {
    return res.status(409).json({
      error: 'رقم البطاقة موجود مسبقاً'
    });
  }

  // Create new client
  const newClient = {
    id: 2,
    clientName,
    appName,
    cardNumber,
    clientCode: 1001,
    status: 1,
    userId: parseInt(userId),
    createdAt: new Date().toISOString()
  };

  res.status(201).json({
    success: true,
    message: 'تم إنشاء العميل بنجاح',
    client: newClient
  });
});

// Agents API
app.get('/api/agents', (req, res) => {
  res.json({
    agents: [
      {
        id: 1,
        agentName: 'وكيل تجريبي',
        agencyName: 'وكالة يمن موبايل',
        agencyType: 'وكيل يمن موبايل',
        isActive: true,
        createdAt: new Date().toISOString()
      }
    ],
    pagination: {
      page: 1,
      limit: 10,
      total: 1,
      totalPages: 1
    }
  });
});

// Data Records API
app.get('/api/data-records', (req, res) => {
  res.json({
    dataRecords: [
      {
        id: 1,
        clientCode: '1000',
        operationStatus: 1,
        operationDate: new Date().toISOString(),
        agentReference: 12345,
        agent: { agentName: 'وكيل تجريبي' },
        client: { clientName: 'عميل تجريبي' }
      }
    ],
    pagination: {
      page: 1,
      limit: 10,
      total: 1,
      totalPages: 1
    }
  });
});

// Security API
app.get('/api/security/stats', (req, res) => {
  res.json({
    totalAttempts: 10,
    successfulLogins: 8,
    failedLogins: 2,
    suspiciousActivity: 0,
    blockedIPs: 0,
    activeDevices: 3,
    uniqueIPs: 5,
    lastSecurityScan: new Date().toISOString(),
    last24Hours: {
      successfulLogins: 5,
      failedAttempts: 1
    },
    systemHealth: {
      database: 'connected',
      api: 'operational',
      security: 'active'
    }
  });
});

app.get('/api/security/login-attempts', (req, res) => {
  res.json({
    attempts: [
      {
        id: 1,
        type: 'success',
        username: 'admin',
        ip: '*************',
        timestamp: new Date().toISOString(),
        deviceId: 'device_001'
      }
    ],
    total: 1,
    page: 1,
    limit: 20,
    totalPages: 1
  });
});

// External API
app.get('/api/external/health', (req, res) => {
  res.json({
    status: 'success',
    message: 'External API is healthy',
    data: {
      timestamp: new Date().toISOString(),
      database: 'connected',
      version: '1.0.0'
    }
  });
});

app.get('/api/external/stats', (req, res) => {
  res.json({
    success: true,
    message: 'System statistics retrieved successfully',
    data: {
      clients: { total: 1, active: 1, inactive: 0 },
      agents: { total: 1, active: 1, inactive: 0 },
      users: { total: 1, active: 1, inactive: 0 },
      operations: { totalRecords: 1 },
      timestamp: new Date().toISOString()
    }
  });
});

// Static files for React app
const clientDistPath = path.join(__dirname, '../client/dist');
console.log('Client dist path:', clientDistPath);

// Check if client dist exists
if (fs.existsSync(clientDistPath)) {
  console.log('✅ Client dist folder found');
  app.use(express.static(clientDistPath));

  // React app fallback
  app.get('*', (req, res) => {
    // Skip API routes
    if (req.path.startsWith('/api/')) {
      return res.status(404).json({ error: 'API endpoint not found' });
    }

    console.log('Serving React app for:', req.path);
    res.sendFile(path.join(clientDistPath, 'index.html'));
  });
} else {
  console.log('❌ Client dist folder not found');

  // Fallback HTML
  app.get('*', (req, res) => {
    if (req.path.startsWith('/api/')) {
      return res.status(404).json({ error: 'API endpoint not found' });
    }

    res.send(`
      <!DOCTYPE html>
      <html lang="ar" dir="rtl">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>نظام إدارة العملاء والوكلاء</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
          }
          .container {
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            max-width: 600px;
            margin: 0 auto;
          }
          h1 { font-size: 2.5em; margin-bottom: 20px; }
          p { font-size: 1.2em; margin-bottom: 15px; }
          .status { color: #4CAF50; font-weight: bold; }
          .links { margin-top: 30px; }
          .links a {
            display: inline-block;
            margin: 10px;
            padding: 12px 24px;
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s;
          }
          .links a:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
          }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>🚀 نظام إدارة العملاء والوكلاء</h1>
          <p class="status">✅ الخادم يعمل بشكل صحيح</p>
          <p>مرحباً بك في نظام إدارة العملاء والوكلاء اليمني</p>
          <p>الخادم متصل وجاهز للاستخدام</p>

          <div class="links">
            <a href="/health">فحص الصحة</a>
            <a href="/api/test">اختبار API</a>
            <a href="/developer-test">صفحة المطورين</a>
          </div>

          <div style="margin-top: 30px; font-size: 0.9em; opacity: 0.8;">
            <p>الوقت: ${new Date().toLocaleString('ar-SA')}</p>
            <p>المنفذ: ${PORT}</p>
          </div>
        </div>

        <script>
          // Test API connection
          fetch('/health')
            .then(response => response.json())
            .then(data => {
              console.log('Health check:', data);
            })
            .catch(error => {
              console.error('Health check failed:', error);
            });
        </script>
      </body>
      </html>
    `);
  });
}

// Error handling
app.use((error, req, res, next) => {
  console.error('Server error:', error.message);
  res.status(500).json({
    error: 'Internal server error',
    message: error.message
  });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`✅ Working Server started successfully!`);
  console.log(`🌐 Local: http://localhost:${PORT}`);
  console.log(`🌍 External: http://***********:${PORT}`);
  console.log(`📋 Health: http://localhost:${PORT}/health`);
  console.log(`🧪 API Test: http://localhost:${PORT}/api/test`);
  console.log(`⏰ Started at: ${new Date().toLocaleString('ar-SA')}`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Server shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Server shutting down gracefully...');
  process.exit(0);
});

module.exports = app;
