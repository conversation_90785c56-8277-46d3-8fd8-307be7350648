const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const morgan = require('morgan');
const session = require('express-session');
const cookieParser = require('cookie-parser');
const path = require('path');
const fs = require('fs');
const cluster = require('cluster');
const os = require('os');
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');
const winston = require('winston');
require('dotenv').config();

// إعداد Winston Logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss'
    }),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'yemclient-server' },
  transports: [
    new winston.transports.File({
      filename: path.join(__dirname, 'logs', 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5
    }),
    new winston.transports.File({
      filename: path.join(__dirname, 'logs', 'combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5
    })
  ]
});

// إضافة console في التطوير
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }));
}

const app = express();
const PORT = process.env.PORT || 8080;
const NODE_ENV = process.env.NODE_ENV || 'development';
const prisma = new PrismaClient({
  log: NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error']
});

// إنشاء مجلد logs
const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

logger.info('🚀 Starting YemClient Production Server', {
  nodeEnv: NODE_ENV,
  port: PORT,
  nodeVersion: process.version,
  platform: process.platform
});

// اختبار الاتصال بقاعدة البيانات
async function testDatabaseConnection() {
  try {
    logger.info('🔗 Testing database connection...');
    await prisma.$connect();
    await prisma.$queryRaw`SELECT 1`;
    logger.info('✅ Database connection successful');
    return true;
  } catch (error) {
    logger.error('❌ Database connection failed', { error: error.message });
    return false;
  }
}

// إعداد Rate Limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.warn('Rate limit exceeded', {
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
    res.status(429).json({
      error: 'Too many requests from this IP, please try again later.',
      retryAfter: '15 minutes'
    });
  }
});

// إعداد Rate Limiting للدخول
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 login requests per windowMs
  message: {
    error: 'Too many login attempts from this IP, please try again later.',
    retryAfter: '15 minutes'
  },
  skipSuccessfulRequests: true
});

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https:"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'", "https:"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false
}));

// CORS configuration
app.use(cors({
  origin: function (origin, callback) {
    const allowedOrigins = [
      'http://localhost:5173',
      'http://localhost:8080',
      'http://**************:8080',
      'http://************:8080',
      'http://***********:8080',
      'https://***********:8080'
    ];

    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      logger.warn('CORS blocked origin', { origin });
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200
}));

// Basic middleware
app.use(compression());
app.use(express.json({
  limit: '10mb',
  verify: (req, res, buf) => {
    req.rawBody = buf;
  }
}));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser());

// Morgan logging
app.use(morgan('combined', {
  stream: {
    write: (message) => logger.info(message.trim())
  }
}));

// Session configuration
app.use(session({
  secret: process.env.SESSION_SECRET || 'yemclient-super-secret-key-2024',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: NODE_ENV === 'production',
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    sameSite: 'strict'
  },
  name: 'yemclient.sid'
}));

// Apply rate limiting
app.use(limiter);

// -------------------- AUTH API --------------------
app.post('/api/auth/login', loginLimiter, async (req, res) => {
  try {
    const { loginName, password, deviceId } = req.body;
    logger.info(`Login attempt for ${loginName}`);

    if (!loginName || !password) {
      return res.status(400).json({ success: false, message: 'بيانات الدخول ناقصة' });
    }

    // Find user by username or loginName
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { username: loginName },
          { loginName: loginName }
        ]
      }
    });

    if (!user) {
      logger.warn(`User not found: ${loginName}`);
      return res.status(401).json({ success: false, message: 'بيانات الدخول غير صحيحة' });
    }

    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      logger.warn(`Invalid password for ${loginName}`);
      await prisma.loginAttempt.create({
        data: {
          userId: user.id,
          ipAddress: req.ip,
          deviceId: deviceId || null,
          success: false,
          userType: 'user'
        }
      });
      return res.status(401).json({ success: false, message: 'بيانات الدخول غير صحيحة' });
    }

    if (!deviceId) {
      return res.status(400).json({ success: false, message: 'Device ID مطلوب للدخول' });
    }

    let deviceAuthorized = false;
    const authorizedDevices = [];

    if (user.device1) {
      authorizedDevices.push(user.device1);
      if (user.device1 === deviceId) {
        deviceAuthorized = true;
      }
    }

    if (!deviceAuthorized && user.deviceId) {
      const oldDevices = user.deviceId.includes(',') ? user.deviceId.split(',').map(d => d.trim()) : [user.deviceId];
      authorizedDevices.push(...oldDevices);
      if (oldDevices.includes(deviceId)) {
        deviceAuthorized = true;
      }
    }

    // Register first device automatically
    if (!user.device1 && !user.deviceId) {
      await prisma.user.update({ where: { id: user.id }, data: { device1: deviceId } });
      deviceAuthorized = true;
      authorizedDevices.push(deviceId);
      logger.info(`Registered first device for ${loginName}`);
    }

    if (!deviceAuthorized) {
      logger.warn(`Device not authorized for ${loginName}`);
      return res.status(403).json({
        success: false,
        message: 'هذا الجهاز غير مصرح له بالدخول',
        authorizedDevices,
        currentDevice: deviceId
      });
    }

    await prisma.loginAttempt.create({
      data: {
        userId: user.id,
        ipAddress: req.ip,
        deviceId,
        success: true,
        userType: 'user'
      }
    });

    // Store session
    req.session.userId = user.id;

    logger.info(`Login success for ${loginName}`);
    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: {
        id: user.id,
        username: user.username,
        loginName: user.loginName,
        permissions: user.permissions
      }
    });
  } catch (err) {
    logger.error('Login error', { error: err.message });
    res.status(500).json({ success: false, message: 'خطأ في الخادم' });
  }
});

// Request logging middleware
app.use((req, res, next) => {
  const startTime = Date.now();

  res.on('finish', () => {
    const duration = Date.now() - startTime;
    logger.info('Request completed', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
  });

  next();
});

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    logger.info('Health check requested');

    // فحص قاعدة البيانات
    await prisma.$queryRaw`SELECT 1`;

    // إحصائيات من قاعدة البيانات
    const [userCount, clientCount, agentCount] = await Promise.all([
      prisma.user.count(),
      prisma.client.count(),
      prisma.agent.count()
    ]);

    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: '2.0.0',
      environment: NODE_ENV,
      database: {
        status: 'connected',
        users: userCount,
        clients: clientCount,
        agents: agentCount
      },
      server: {
        nodeVersion: process.version,
        platform: process.platform,
        pid: process.pid
      }
    };

    logger.info('Health check successful');
    res.json(healthData);
  } catch (error) {
    logger.error('Health check failed', { error: error.message });
    res.status(500).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Ready check for PM2
app.get('/ready', async (req, res) => {
  try {
    await prisma.$queryRaw`SELECT 1`;
    res.status(200).send('OK');
  } catch (error) {
    res.status(503).send('Service Unavailable');
  }
});

// Metrics endpoint
app.get('/metrics', (req, res) => {
  const metrics = {
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    cpu: process.cpuUsage(),
    version: process.version,
    platform: process.platform,
    pid: process.pid,
    environment: NODE_ENV
  };

  res.json(metrics);
});

// ==================== AUTH API ====================
app.post('/api/auth/login', loginLimiter, async (req, res) => {
  try {
    const { loginName, password, deviceId, userType = 'auto' } = req.body;
    const clientIP = req.ip;
    const userAgent = req.get('User-Agent');

    logger.info('Login attempt', {
      loginName,
      userType,
      ip: clientIP,
      userAgent: userAgent?.substring(0, 100)
    });

    if (!loginName || !password) {
      logger.warn('Login missing credentials', { loginName, ip: clientIP });
      return res.status(400).json({
        success: false,
        message: 'اسم المستخدم وكلمة المرور مطلوبان'
      });
    }

    let user = null;
    let accountType = null;

    // البحث في جدول المستخدمين
    if (userType === 'auto' || userType === 'user') {
      try {
        user = await prisma.user.findUnique({
          where: { loginName }
        });
        if (user) {
          accountType = 'user';
          logger.info('User found in database', {
            userId: user.id,
            username: user.username
          });
        }
      } catch (error) {
        logger.error('Error searching users', { error: error.message });
      }
    }

    // البحث في جدول العملاء
    if (!user && (userType === 'auto' || userType === 'client')) {
      try {
        user = await prisma.client.findFirst({
          where: {
            OR: [
              { clientCode: parseInt(loginName) || 0 },
              { cardNumber: loginName }
            ]
          }
        });
        if (user) {
          accountType = 'client';
          logger.info('Client found in database', {
            clientId: user.id,
            clientName: user.clientName
          });
        }
      } catch (error) {
        logger.error('Error searching clients', { error: error.message });
      }
    }

    if (!user) {
      logger.warn('Account not found', { loginName, ip: clientIP });

      // تسجيل محاولة دخول فاشلة
      try {
        await prisma.loginAttempt.create({
          data: {
            userType: accountType || 'unknown',
            deviceId: deviceId || 'unknown',
            ipAddress: clientIP,
            userAgent: userAgent || 'unknown',
            success: false
          }
        });
      } catch (logError) {
        logger.error('Failed to log failed attempt', { error: logError.message });
      }

      return res.status(401).json({
        success: false,
        message: 'بيانات الدخول غير صحيحة'
      });
    }

    // التحقق من كلمة المرور
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      logger.warn('Invalid password', {
        loginName,
        accountType,
        ip: clientIP
      });

      // تسجيل محاولة دخول فاشلة
      try {
        await prisma.loginAttempt.create({
          data: {
            userType: accountType,
            userId: accountType === 'user' ? user.id : null,
            deviceId: deviceId || 'unknown',
            ipAddress: clientIP,
            userAgent: userAgent || 'unknown',
            success: false
          }
        });
      } catch (logError) {
        logger.error('Failed to log failed attempt', { error: logError.message });
      }

      return res.status(401).json({
        success: false,
        message: 'بيانات الدخول غير صحيحة'
      });
    }

    // التحقق من حالة الحساب
    const isActive = accountType === 'user' ? user.isActive : user.status === 1;
    if (!isActive) {
      logger.warn('Account inactive', { loginName, accountType });
      return res.status(403).json({
        success: false,
        message: 'الحساب غير مفعل'
      });
    }

    // تسجيل محاولة دخول ناجحة
    try {
      await prisma.loginAttempt.create({
        data: {
          userType: accountType,
          userId: accountType === 'user' ? user.id : null,
          deviceId: deviceId || 'unknown',
          ipAddress: clientIP,
          userAgent: userAgent || 'unknown',
          success: true
        }
      });
    } catch (logError) {
      logger.error('Failed to log successful attempt', { error: logError.message });
    }

    // إعداد بيانات الاستجابة
    let responseData;
    if (accountType === 'user') {
      responseData = {
        id: user.id,
        username: user.username,
        loginName: user.loginName,
        permissions: user.permissions || {},
        deviceId: user.deviceId,
        isActive: user.isActive,
        accountType: 'user'
      };
    } else {
      responseData = {
        id: user.id,
        username: user.clientName,
        loginName: user.clientCode.toString(),
        clientCode: user.clientCode,
        appName: user.appName,
        isActive: user.status === 1,
        accountType: 'client'
      };
    }

    logger.info('Login successful', {
      accountType,
      id: user.id,
      name: accountType === 'user' ? user.username : user.clientName,
      ip: clientIP
    });

    // حفظ في الجلسة
    req.session.user = responseData;

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: responseData
    });

  } catch (error) {
    logger.error('Login error', {
      error: error.message,
      stack: error.stack,
      ip: req.ip
    });
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم'
    });
  }
});

app.post('/api/auth/logout', (req, res) => {
  logger.info('Logout requested', {
    userId: req.session.user?.id,
    ip: req.ip
  });

  req.session.destroy((err) => {
    if (err) {
      logger.error('Logout error', { error: err.message });
      return res.status(500).json({
        success: false,
        message: 'خطأ في تسجيل الخروج'
      });
    }

      res.clearCookie('yemclient.sid');
    res.json({
      success: true,
      message: 'تم تسجيل الخروج بنجاح'
    });
  });
});

// ==================== USERS API ====================
app.get('/api/users', async (req, res) => {
  try {
    logger.info('Users list requested', { ip: req.ip });

    const { page = 1, limit = 10, search = '' } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const where = search ? {
      OR: [
        { username: { contains: search, mode: 'insensitive' } },
        { loginName: { contains: search, mode: 'insensitive' } }
      ]
    } : {};

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip,
        take: parseInt(limit),
        select: {
          id: true,
          username: true,
          loginName: true,
          deviceId: true,
          device1: true,
          permissions: true,
          isActive: true,
          createdAt: true,
          _count: {
            select: { clients: true }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.user.count({ where })
    ]);

    logger.info('Users list generated', { count: users.length, total });

    res.json({
      users,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    logger.error('Users list error', { error: error.message });
    res.status(500).json({ error: 'فشل في جلب قائمة المستخدمين' });
  }
});

// ==================== CLIENTS API ====================
app.get('/api/clients', async (req, res) => {
  try {
    logger.info('Clients list requested', { ip: req.ip });

    const { page = 1, limit = 10, search = '', status, userId } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const where = {};

    if (search) {
      where.OR = [
        { clientName: { contains: search, mode: 'insensitive' } },
        { clientCode: { equals: parseInt(search) || 0 } },
        { cardNumber: { contains: search } }
      ];
    }

    if (status) {
      where.status = parseInt(status);
    }

    if (userId) {
      where.userId = parseInt(userId);
    }

    const [clients, total] = await Promise.all([
      prisma.client.findMany({
        where,
        skip,
        take: parseInt(limit),
        include: {
          user: {
            select: {
              id: true,
              username: true,
              loginName: true
            }
          },
          _count: {
            select: { dataRecords: true }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.client.count({ where })
    ]);

    logger.info('Clients list generated', { count: clients.length, total });

    res.json({
      clients,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    logger.error('Clients list error', { error: error.message });
    res.status(500).json({ error: 'فشل في جلب قائمة العملاء' });
  }
});

// ==================== AGENTS API ====================
app.get('/api/agents', async (req, res) => {
  try {
    logger.info('Agents list requested', { ip: req.ip });

    const { page = 1, limit = 10, search = '', agencyType } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const where = {};

    if (search) {
      where.OR = [
        { agentName: { contains: search, mode: 'insensitive' } },
        { agencyName: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (agencyType) {
      where.agencyType = agencyType;
    }

    const [agents, total] = await Promise.all([
      prisma.agent.findMany({
        where,
        skip,
        take: parseInt(limit),
        include: {
          _count: {
            select: { dataRecords: true }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.agent.count({ where })
    ]);

    logger.info('Agents list generated', { count: agents.length, total });

    res.json({
      agents,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    logger.error('Agents list error', { error: error.message });
    res.status(500).json({ error: 'فشل في جلب قائمة الوكلاء' });
  }
});

// ==================== DATA RECORDS API ====================
app.get('/api/data-records', async (req, res) => {
  try {
    logger.info('Data records requested', { ip: req.ip });

    const { page = 1, limit = 10, search = '', status } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const where = {};

    if (search) {
      where.OR = [
        { clientCode: { contains: search, mode: 'insensitive' } },
        { agentReference: { equals: parseInt(search) || 0 } }
      ];
    }

    if (status) {
      where.operationStatus = parseInt(status);
    }

    const [dataRecords, total] = await Promise.all([
      prisma.dataRecord.findMany({
        where,
        skip,
        take: parseInt(limit),
        include: {
          agent: {
            select: {
              id: true,
              agentName: true,
              agencyName: true,
              agencyType: true
            }
          },
          client: {
            select: {
              id: true,
              clientName: true,
              clientCode: true,
              appName: true
            }
          }
        },
        orderBy: { operationDate: 'desc' }
      }),
      prisma.dataRecord.count({ where })
    ]);

    logger.info('Data records generated', { count: dataRecords.length, total });

    res.json({
      dataRecords,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    logger.error('Data records error', { error: error.message });
    res.status(500).json({ error: 'فشل في جلب سجلات البيانات' });
  }
});

// ==================== SECURITY API ====================
app.get('/api/security/stats', async (req, res) => {
  try {
    logger.info('Security stats requested', { ip: req.ip });

    const [totalUsers, activeUsers, totalClients, activeClients, totalAgents, activeAgents, totalAttempts, successfulAttempts] = await Promise.all([
      prisma.user.count(),
      prisma.user.count({ where: { isActive: true } }),
      prisma.client.count(),
      prisma.client.count({ where: { status: 1 } }),
      prisma.agent.count(),
      prisma.agent.count({ where: { isActive: true } }),
      prisma.loginAttempt.count(),
      prisma.loginAttempt.count({ where: { success: true } })
    ]);

    const stats = {
      totalAttempts,
      successfulLogins: successfulAttempts,
      failedLogins: totalAttempts - successfulAttempts,
      suspiciousActivity: 0,
      blockedIPs: 0,
      activeDevices: activeUsers,
      uniqueIPs: activeUsers + activeAgents,
      lastSecurityScan: new Date().toISOString(),
      last24Hours: {
        successfulLogins: successfulAttempts,
        failedAttempts: Math.floor((totalAttempts - successfulAttempts) * 0.1)
      },
      systemHealth: {
        database: 'connected',
        api: 'operational',
        security: 'active'
      },
      summary: {
        totalUsers,
        activeUsers,
        totalClients,
        activeClients,
        totalAgents,
        activeAgents
      }
    };

    logger.info('Security stats generated');
    res.json(stats);
  } catch (error) {
    logger.error('Security stats error', { error: error.message });
    res.status(500).json({ error: 'Failed to fetch security stats' });
  }
});

// ==================== DASHBOARD API ====================
app.get('/api/dashboard/stats', async (req, res) => {
  try {
    logger.info('Dashboard stats requested', { ip: req.ip });

    const [totalUsers, totalClients, totalAgents, totalDataRecords, totalSecurityRecords] = await Promise.all([
      prisma.user.count(),
      prisma.client.count(),
      prisma.agent.count(),
      prisma.dataRecord.count(),
      prisma.loginAttempt.count()
    ]);

    const activeUsers = await prisma.user.count({ where: { isActive: true } });

    res.json({
      totalUsers,
      totalClients,
      totalAgents,
      totalDataRecords,
      totalSecurityRecords,
      activeUsers,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Dashboard stats error', { error: error.message });
    res.status(500).json({ error: 'Failed to fetch dashboard stats' });
  }
});

app.get('/api/dashboard/recent-activity', async (req, res) => {
  try {
    logger.info('Recent activity requested', { ip: req.ip });

    const recentLogins = await prisma.loginAttempt.findMany({
      take: 10,
      orderBy: { timestamp: 'desc' },
      include: {
        user: { select: { username: true, loginName: true } },
        client: { select: { clientName: true, clientCode: true } }
      }
    });

    const activities = recentLogins.map(attempt => ({
      id: attempt.id,
      success: attempt.success,
      userType: attempt.userType,
      username: attempt.user?.username || attempt.client?.clientName || 'unknown',
      loginName: attempt.user?.loginName || attempt.client?.clientCode?.toString() || 'unknown',
      ipAddress: attempt.ipAddress,
      deviceId: attempt.deviceId,
      timestamp: attempt.timestamp
    }));

    res.json(activities);
  } catch (error) {
    logger.error('Recent activity error', { error: error.message });
    res.status(500).json({ error: 'Failed to fetch recent activity' });
  }
});

// Static files for React app
const clientDistPath = path.join(__dirname, '../client/dist');

if (fs.existsSync(clientDistPath)) {
  logger.info('Client dist folder found, serving static files');
  app.use(express.static(clientDistPath, {
    maxAge: NODE_ENV === 'production' ? '1d' : '0',
    etag: true,
    lastModified: true
  }));

  // React app fallback
  app.get('*', (req, res) => {
    if (req.path.startsWith('/api/')) {
      return res.status(404).json({ error: 'API endpoint not found' });
    }

    res.sendFile(path.join(clientDistPath, 'index.html'));
  });
} else {
  logger.warn('Client dist folder not found');
  app.get('*', (req, res) => {
    if (req.path.startsWith('/api/')) {
      return res.status(404).json({ error: 'API endpoint not found' });
    }
    res.status(503).send('<h1>Application not built. Please run: npm run build</h1>');
  });
}

// Global error handler
app.use((error, req, res, next) => {
  logger.error('Unhandled error', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip
  });

  res.status(500).json({
    error: 'Internal server error',
    message: NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

// 404 handler
app.use((req, res) => {
  logger.warn('404 Not Found', {
    url: req.url,
    method: req.method,
    ip: req.ip
  });

  res.status(404).json({
    error: 'Not Found',
    message: 'The requested resource was not found'
  });
});

// Graceful shutdown
async function gracefulShutdown(signal) {
  logger.info(`Received ${signal}, starting graceful shutdown`);

  try {
    await prisma.$disconnect();
    logger.info('Database connection closed');

    process.exit(0);
  } catch (error) {
    logger.error('Error during shutdown', { error: error.message });
    process.exit(1);
  }
}

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Unhandled promise rejection
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection', { reason, promise });
});

// Uncaught exception
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception', { error: error.message, stack: error.stack });
  process.exit(1);
});

// بدء الخادم
async function startProductionServer() {
  try {
    logger.info('Starting production server initialization');

    // اختبار الاتصال بقاعدة البيانات
    const dbConnected = await testDatabaseConnection();

    if (!dbConnected) {
      logger.error('Cannot start server without database connection');
      process.exit(1);
    }

    // بدء الخادم
    const server = app.listen(PORT, '0.0.0.0', () => {
      logger.info('Production server started successfully', {
        port: PORT,
        environment: NODE_ENV,
        nodeVersion: process.version,
        pid: process.pid
      });

      // إشارة لـ PM2 أن الخادم جاهز
      if (process.send) {
        process.send('ready');
      }
    });

    // Handle server errors
    server.on('error', (error) => {
      logger.error('Server error', { error: error.message });
      process.exit(1);
    });

    return server;

  } catch (error) {
    logger.error('Failed to start production server', {
      error: error.message,
      stack: error.stack
    });
    process.exit(1);
  }
}

// بدء الخادم
startProductionServer();

module.exports = app;
