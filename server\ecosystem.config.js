module.exports = {
  apps: [
    {
      name: 'yemclient-server',
      script: './production-server.js',
      instances: "max",
      exec_mode: 'cluster',
      watch: process.env.NODE_ENV !== 'production',
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'development',
        PORT: 8080
      },
      env_staging: {
        NODE_ENV: 'staging',
        PORT: 8080
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 8080,
        watch: false
      },
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      error_file: './logs/pm2-error.log',
      out_file: './logs/pm2-out.log',
      log_file: './logs/pm2-combined.log',
      time: true,
      autorestart: true,
      restart_delay: 1000,
      max_restarts: 10,
      min_uptime: '10s',
      kill_timeout: 5000,
      listen_timeout: 3000,
      shutdown_with_message: true,
      wait_ready: true,
      instance_var: 'INSTANCE_ID'
    },
    {
      name: 'yemclient-api',
      script: './api-server.js',
      instances: "max",
      exec_mode: 'cluster',
      watch: false,
      max_memory_restart: '512M',
      env: {
        NODE_ENV: 'development',
        PORT: 8081
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 8081,
        watch: false
      },
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      error_file: './logs/pm2-error.log',
      out_file: './logs/pm2-out.log',
      log_file: './logs/pm2-combined.log',
      time: true,
      autorestart: true,
      restart_delay: 1000,
      max_restarts: 10,
      min_uptime: '10s',
      kill_timeout: 5000,
      listen_timeout: 3000,
      shutdown_with_message: true,
      wait_ready: true,
      instance_var: 'API_INSTANCE_ID'
    }
  ],

  deploy: {
    production: {
      user: 'node',
      host: '***********',
      ref: 'origin/main',
      repo: '**************:repo.git',
      path: '/var/www/yemclient',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};
