import React, { useState } from 'react'
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tooltip
} from '@mui/material'
import {
  Add,
  Edit,
  Delete,
  Search,
  Visibility,
  CheckCircle,
  Cancel
} from '@mui/icons-material'
import { DataGrid } from '@mui/x-data-grid'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { useAuth } from '../contexts/AuthContext'
import { useSnackbar } from 'notistack'
import AgentForm from '../components/forms/AgentForm'
import EnhancedButton from '../components/common/EnhancedButton'

const AgentsPage = () => {
  const [page, setPage] = useState(0)
  const [pageSize, setPageSize] = useState(10)
  const [searchTerm, setSearchTerm] = useState('')
  const [dialogOpen, setDialogOpen] = useState(false)
  const [selectedAgent, setSelectedAgent] = useState(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [agentToDelete, setAgentToDelete] = useState(null)

  const { api, hasPermission } = useAuth()
  const { enqueueSnackbar } = useSnackbar()
  const queryClient = useQueryClient()

  // جلب بيانات الوكلاء من API
  const { data, isLoading, refetch } = useQuery(
    ['agents', page, pageSize, searchTerm],
    () => api.get('/api/agents', {
      params: {
        page: page + 1,
        limit: pageSize,
        search: searchTerm
      }
    }).then(res => res.data),
    {
      keepPreviousData: true,
      staleTime: 30000,
      onError: (error) => {
        console.error('Error fetching agents:', error)
        enqueueSnackbar('خطأ في جلب بيانات الوكلاء', { variant: 'error' })
      }
    }
  )

  // حذف وكيل
  const deleteMutation = useMutation(
    (agentId) => api.delete(`/api/agents/${agentId}`),
    {
      onSuccess: () => {
        enqueueSnackbar('تم حذف الوكيل بنجاح', { variant: 'success' })
        queryClient.invalidateQueries('agents')
        setDeleteDialogOpen(false)
        setAgentToDelete(null)
      },
      onError: (error) => {
        console.error('Error deleting agent:', error)
        enqueueSnackbar('خطأ في حذف الوكيل', { variant: 'error' })
      }
    }
  )

  const columns = [
    {
      field: 'id',
      headerName: 'المعرف',
      width: 80
    },
    {
      field: 'agentName',
      headerName: 'اسم الوكيل',
      width: 200
    },
    {
      field: 'agencyName',
      headerName: 'اسم الوكالة',
      width: 200
    },
    {
      field: 'agencyType',
      headerName: 'نوع الوكالة',
      width: 150
    },
    {
      field: 'ipAddress',
      headerName: 'عنوان IP',
      width: 140
    },
    {
      field: 'loginName',
      headerName: 'اسم الدخول',
      width: 150,
      renderCell: (params) => (
        <Chip
          label={params.value || 'غير محدد'}
          color="info"
          variant="outlined"
          size="small"
        />
      )
    },
    {
      field: 'loginPassword',
      headerName: 'كلمة مرور الدخول',
      width: 180,
      renderCell: (params) => (
        <Chip
          label={params.value ? '••••••••' : 'غير محدد'}
          color={params.value ? 'success' : 'default'}
          variant="outlined"
          size="small"
        />
      )
    },
    {
      field: 'isActive',
      headerName: 'الحالة',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={params.value ? 'نشط' : 'غير نشط'}
          color={params.value ? 'success' : 'default'}
          size="small"
          icon={params.value ? <CheckCircle /> : <Cancel />}
        />
      )
    },
    {
      field: '_count',
      headerName: 'عدد العملاء',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value?.clients || 0}
          color="primary"
          variant="outlined"
          size="small"
        />
      )
    },
    {
      field: 'actions',
      headerName: 'الإجراءات',
      width: 200,
      sortable: false,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center', alignItems: 'center' }}>
          <Tooltip title="عرض" arrow>
            <IconButton
              size="small"
              onClick={() => handleView(params.row)}
              sx={{
                color: 'primary.main',
                backgroundColor: 'primary.light',
                '&:hover': { backgroundColor: 'primary.main', color: 'white' },
                borderRadius: '8px',
                padding: '8px'
              }}
            >
              <span style={{ fontSize: '18px' }}>👁️</span>
            </IconButton>
          </Tooltip>
          <Tooltip title="تعديل" arrow>
            <IconButton
              size="small"
              onClick={() => handleEdit(params.row)}
              sx={{
                color: 'warning.main',
                backgroundColor: 'warning.light',
                '&:hover': { backgroundColor: 'warning.main', color: 'white' },
                borderRadius: '8px',
                padding: '8px'
              }}
            >
              <span style={{ fontSize: '18px' }}>✏️</span>
            </IconButton>
          </Tooltip>
          <Tooltip title="حذف" arrow>
            <IconButton
              size="small"
              onClick={() => handleDelete(params.row)}
              sx={{
                color: 'error.main',
                backgroundColor: 'error.light',
                '&:hover': { backgroundColor: 'error.main', color: 'white' },
                borderRadius: '8px',
                padding: '8px'
              }}
            >
              <span style={{ fontSize: '18px' }}>🗑️</span>
            </IconButton>
          </Tooltip>
        </Box>
      )
    }
  ]

  const handleView = (agent) => {
    setSelectedAgent(agent)
    setDialogOpen(true)
  }

  const handleEdit = (agent) => {
    setSelectedAgent(agent)
    setDialogOpen(true)
  }

  const handleDelete = (agent) => {
    setAgentToDelete(agent)
    setDeleteDialogOpen(true)
  }

  const handleAdd = () => {
    setSelectedAgent(null)
    setDialogOpen(true)
  }

  const handleCloseDialog = () => {
    setDialogOpen(false)
    setSelectedAgent(null)
  }

  const handleConfirmDelete = () => {
    if (agentToDelete) {
      deleteMutation.mutate(agentToDelete.id)
    }
  }

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 700 }}>
        إدارة الوكلاء
      </Typography>

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="البحث في الوكلاء..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <span style={{ marginRight: '8px', fontSize: '18px' }}>🔍</span>
                }}
              />
            </Grid>
            <Grid item xs={12} md={6} sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
              <Button
                variant="contained"
                onClick={handleAdd}
                color="primary"
                sx={{ minWidth: '180px' }}
              >
                🏢 إضافة وكيل جديد
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Card>
        <CardContent>
          <Box sx={{ height: 600, width: '100%' }}>
            <DataGrid
              rows={data?.data || []}
              columns={columns}
              pageSize={pageSize}
              onPageSizeChange={setPageSize}
              rowsPerPageOptions={[5, 10, 25, 50]}
              page={page}
              onPageChange={setPage}
              rowCount={data?.total || 0}
              paginationMode="server"
              loading={isLoading}
              disableSelectionOnClick
              localeText={{
                noRowsLabel: 'لا توجد بيانات',
                footerRowSelected: (count) => `${count} صف محدد`,
                footerTotalRows: 'إجمالي الصفوف:',
                footerPaginationRowsPerPage: 'صفوف لكل صفحة:',
              }}
            />
          </Box>
        </CardContent>
      </Card>

      {/* Dialog للإضافة/التعديل */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">
            {selectedAgent ? 'تعديل الوكيل' : 'إضافة وكيل جديد'}
          </Typography>
          <IconButton onClick={handleCloseDialog} size="small">
            <span style={{ fontSize: '20px' }}>✕</span>
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <AgentForm
            agent={selectedAgent}
            onSuccess={() => {
              queryClient.invalidateQueries('agents')
              handleCloseDialog()
            }}
            readOnly={!hasPermission('agents', selectedAgent ? 'update' : 'create')}
          />
        </DialogContent>
      </Dialog>

      {/* Dialog تأكيد الحذف */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">تأكيد الحذف</Typography>
          <IconButton onClick={() => setDeleteDialogOpen(false)} size="small">
            <span style={{ fontSize: '20px' }}>✕</span>
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Typography>
            هل أنت متأكد من حذف الوكيل "{agentToDelete?.agentName}"؟
          </Typography>
          {agentToDelete?._count?.dataRecords > 0 && (
            <Typography color="error" sx={{ mt: 1 }}>
              تحذير: هذا الوكيل لديه {agentToDelete._count.dataRecords} سجل بيانات مرتبط به.
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>إلغاء</Button>
          <Button
            onClick={handleConfirmDelete}
            color="error"
            variant="contained"
            disabled={deleteMutation.isLoading}
          >
            حذف
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default AgentsPage
