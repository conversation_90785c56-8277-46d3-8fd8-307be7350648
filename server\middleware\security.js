const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const crypto = require('crypto');

// معدل الطلبات المحدود
const createRateLimiter = (windowMs, max, message) => {
  return rateLimit({
    windowMs: windowMs,
    max: max,
    message: { error: message },
    standardHeaders: false, // إخفاء headers الأمان
    legacyHeaders: false,
    handler: (req, res) => {
      // تسجيل محاولات الاختراق
      console.log(`🚨 محاولة اختراق محتملة من IP: ${req.ip} - ${new Date().toISOString()}`);
      res.status(429).json({ error: 'طلبات كثيرة جداً' });
    }
  });
};

// حد عام للطلبات
const generalLimiter = createRateLimiter(
  15 * 60 * 1000, // 15 دقيقة
  100, // 100 طلب كحد أقصى
  'تم تجاوز الحد المسموح من الطلبات'
);

// حد صارم لتسجيل الدخول
const loginLimiter = createRateLimiter(
  15 * 60 * 1000, // 15 دقيقة
  5, // 5 محاولات فقط
  'تم تجاوز محاولات تسجيل الدخول المسموحة'
);

// حد للعمليات الحساسة
const sensitiveOperationsLimiter = createRateLimiter(
  5 * 60 * 1000, // 5 دقائق
  10, // 10 عمليات فقط
  'تم تجاوز الحد المسموح للعمليات الحساسة'
);

// إعدادات Helmet المتقدمة
const helmetConfig = helmet({
  // إخفاء معلومات الخادم
  hidePoweredBy: true,
  
  // منع clickjacking
  frameguard: { action: 'deny' },
  
  // منع MIME type sniffing
  noSniff: true,
  
  // فرض HTTPS
  hsts: {
    maxAge: 31536000, // سنة واحدة
    includeSubDomains: true,
    preload: true
  },
  
  // Content Security Policy صارم
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https:"],
      fontSrc: ["'self'", "https:", "data:"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
      baseUri: ["'self'"],
      formAction: ["'self'"],
      frameAncestors: ["'self'"]
    }
  },
  
  // منع الإشارة للمصدر
  referrerPolicy: { policy: 'no-referrer' },
  
  // منع تحميل المحتوى غير الآمن
  crossOriginEmbedderPolicy: false // تعطيل لتجنب مشاكل التوافق
});

// middleware لإخفاء معلومات الخادم
const hideServerInfo = (req, res, next) => {
  // إزالة headers تكشف معلومات
  res.removeHeader('X-Powered-By');
  res.removeHeader('Server');
  res.removeHeader('X-AspNet-Version');
  res.removeHeader('X-AspNetMvc-Version');
  
  // إضافة headers مضللة
  res.setHeader('Server', 'Apache/2.4.41');
  res.setHeader('X-Powered-By', 'PHP/7.4.3');
  
  next();
};

// middleware لمنع الوصول للملفات الحساسة
const blockSensitiveFiles = (req, res, next) => {
  const blockedPaths = [
    '/.env',
    '/package.json',
    '/package-lock.json',
    '/node_modules',
    '/.git',
    '/prisma',
    '/logs',
    '/config',
    '/.vscode',
    '/src',
    '/server.js',
    '/app.js'
  ];
  
  const requestPath = req.path.toLowerCase();
  
  if (blockedPaths.some(blocked => requestPath.includes(blocked))) {
    console.log(`🚨 محاولة وصول لملف حساس: ${req.path} من IP: ${req.ip}`);
    return res.status(404).json({ error: 'الصفحة غير موجودة' });
  }
  
  next();
};

// middleware لمنع SQL Injection
const preventSQLInjection = (req, res, next) => {
  const sqlPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
    /(\b(OR|AND)\s+\d+\s*=\s*\d+)/i,
    /(\'|\"|;|--|\*|\|)/,
    /(\bUNION\b.*\bSELECT\b)/i,
    /(\bSELECT\b.*\bFROM\b)/i
  ];
  
  const checkForSQLInjection = (obj) => {
    for (let key in obj) {
      if (typeof obj[key] === 'string') {
        for (let pattern of sqlPatterns) {
          if (pattern.test(obj[key])) {
            console.log(`🚨 محاولة SQL Injection من IP: ${req.ip} - البيانات: ${obj[key]}`);
            return true;
          }
        }
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        if (checkForSQLInjection(obj[key])) {
          return true;
        }
      }
    }
    return false;
  };
  
  if (checkForSQLInjection(req.query) || checkForSQLInjection(req.body)) {
    return res.status(400).json({ error: 'بيانات غير صحيحة' });
  }
  
  next();
};

// middleware لمنع XSS
const preventXSS = (req, res, next) => {
  const xssPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /<img[^>]+src[\\s]*=[\\s]*["\']javascript:/gi
  ];
  
  const sanitizeInput = (obj) => {
    for (let key in obj) {
      if (typeof obj[key] === 'string') {
        for (let pattern of xssPatterns) {
          if (pattern.test(obj[key])) {
            console.log(`🚨 محاولة XSS من IP: ${req.ip} - البيانات: ${obj[key]}`);
            obj[key] = obj[key].replace(pattern, '');
          }
        }
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        sanitizeInput(obj[key]);
      }
    }
  };
  
  sanitizeInput(req.query);
  sanitizeInput(req.body);
  
  next();
};

// middleware لتتبع الطلبات المشبوهة
const trackSuspiciousActivity = (req, res, next) => {
  const suspiciousPatterns = [
    /admin/i,
    /phpmyadmin/i,
    /wp-admin/i,
    /\.php$/i,
    /\.asp$/i,
    /\.jsp$/i,
    /config/i,
    /backup/i,
    /database/i,
    /\.sql$/i,
    /\.bak$/i
  ];
  
  const userAgent = req.get('User-Agent') || '';
  const suspiciousAgents = [
    /sqlmap/i,
    /nikto/i,
    /nmap/i,
    /masscan/i,
    /zap/i,
    /burp/i
  ];
  
  // فحص المسار
  if (suspiciousPatterns.some(pattern => pattern.test(req.path))) {
    console.log(`🚨 طلب مشبوه للمسار: ${req.path} من IP: ${req.ip}`);
  }
  
  // فحص User Agent
  if (suspiciousAgents.some(pattern => pattern.test(userAgent))) {
    console.log(`🚨 User Agent مشبوه: ${userAgent} من IP: ${req.ip}`);
    return res.status(403).json({ error: 'وصول مرفوض' });
  }
  
  next();
};

// middleware لإضافة nonce للـ CSP
const addCSPNonce = (req, res, next) => {
  res.locals.nonce = crypto.randomBytes(16).toString('base64');
  next();
};

// middleware لحماية من CSRF
const csrfProtection = (req, res, next) => {
  // تجاهل GET requests
  if (req.method === 'GET') {
    return next();
  }
  
  const token = req.headers['x-csrf-token'] || req.body._csrf;
  const sessionToken = req.session?.csrfToken;
  
  if (!token || !sessionToken || token !== sessionToken) {
    console.log(`🚨 محاولة CSRF من IP: ${req.ip}`);
    return res.status(403).json({ error: 'طلب غير صحيح' });
  }
  
  next();
};

// middleware لحماية من Path Traversal
const preventPathTraversal = (req, res, next) => {
  const pathTraversalPatterns = [
    /\.\./,
    /\.\\/,
    /\.\/\./,
    /%2e%2e/i,
    /%252e%252e/i,
    /\.\.\\/,
    /\.\.\//
  ];
  
  const checkPath = (path) => {
    return pathTraversalPatterns.some(pattern => pattern.test(path));
  };
  
  if (checkPath(req.path) || checkPath(req.url)) {
    console.log(`🚨 محاولة Path Traversal من IP: ${req.ip} - المسار: ${req.path}`);
    return res.status(400).json({ error: 'مسار غير صحيح' });
  }
  
  next();
};

// middleware لإخفاء أخطاء التطبيق
const hideApplicationErrors = (err, req, res, next) => {
  // تسجيل الخطأ الحقيقي
  console.error('خطأ في التطبيق:', err);
  
  // إرجاع رسالة عامة للمستخدم
  res.status(500).json({
    error: 'حدث خطأ في الخادم',
    timestamp: new Date().toISOString(),
    requestId: crypto.randomUUID()
  });
};

module.exports = {
  helmetConfig,
  generalLimiter,
  loginLimiter,
  sensitiveOperationsLimiter,
  hideServerInfo,
  blockSensitiveFiles,
  preventSQLInjection,
  preventXSS,
  trackSuspiciousActivity,
  addCSPNonce,
  csrfProtection,
  preventPathTraversal,
  hideApplicationErrors
};
